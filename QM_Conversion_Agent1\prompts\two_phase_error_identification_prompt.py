"""
Two-phase prompts for error identification in database conversion.
"""
from typing import List

def create_phase1_error_identification_prompt(target_statements: List[str], error_message: str, target_code: str = None, previous_feedback: str = None) -> str:
    """
    Phase 1: Identify the primary error statement causing the deployment error with spatial correlation.
    """
    statements_text = "\n".join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)])

    # Extract PostgreSQL line number and content for spatial correlation
    spatial_analysis = ""
    import re
    line_match = re.search(r'LINE (\d+):', error_message)
    if line_match and target_code:
        pg_line_number = int(line_match.group(1))
        target_lines = target_code.split('\n')
        if pg_line_number <= len(target_lines):
            error_line_content = target_lines[pg_line_number - 1].strip()

            # Get context around error line (±2 lines)
            context_start = max(0, pg_line_number - 3)
            context_end = min(len(target_lines), pg_line_number + 2)
            context_lines = target_lines[context_start:context_end]
            context_block = '\n'.join([f"Line {context_start + i + 1}: {line}" for i, line in enumerate(context_lines)])

            spatial_analysis = f"""
POSTGRESQL ERROR LOCATION ANALYSIS:
- PostgreSQL Error Line Number: {pg_line_number}
- Exact Content at Error Line {pg_line_number}: "{error_line_content}"
- Context Around Error Line:
{context_block}

CRITICAL SPATIAL CORRELATION TASK:
Find the TARGET STATEMENT that contains this exact line content: "{error_line_content}"
This is the PRIMARY identification criterion - the statement MUST contain this exact line.
"""
        else:
            spatial_analysis = f"""
POSTGRESQL ERROR LOCATION ANALYSIS:
- PostgreSQL Error Line Number: {pg_line_number}
- Error: Line number {pg_line_number} exceeds target code length ({len(target_lines)} lines)
- Fall back to pattern matching approach
"""
    else:
        spatial_analysis = """
POSTGRESQL ERROR LOCATION ANALYSIS:
- No line number found in error message or target code not provided
- Use pattern matching approach with lower confidence
"""

    # Add feedback section if provided
    feedback_section = ""
    if previous_feedback:
        feedback_section = f"""
PREVIOUS IDENTIFICATION FEEDBACK:
The previous error identification was rejected with this feedback:
{previous_feedback}

Please address these specific issues:
- Review the error analysis more carefully
- Ensure accurate line number and statement matching
- Focus on the exact error described in the message
- Verify spatial correlation between PostgreSQL line and statement content
- Provide more detailed technical reasoning

"""

    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. Your task is to identify the PRIMARY statement causing the deployment error during Oracle to PostgreSQL migration.

{feedback_section}

ERROR MESSAGE:
{error_message}

{spatial_analysis}

TARGET STATEMENTS:
{statements_text}

ENHANCED PHASE 1 ANALYSIS - SPATIAL CORRELATION + ERROR IDENTIFICATION:

STEP 1: SPATIAL CORRELATION (HIGHEST PRIORITY)
- If PostgreSQL line number is available, find the statement containing the exact error line content
- Look through each statement to see which one contains the specific line content from the error
- This is the MOST RELIABLE method for error identification
- Only proceed to pattern matching if no exact spatial match is found

STEP 2: ERROR MESSAGE ANALYSIS
- Parse the error message for specific details: error type, keywords, character positions
- Identify the exact error category (syntax, type, constraint, etc.)
- Extract key error indicators and patterns

STEP 3: STATEMENT CONTENT VERIFICATION
- For the spatially identified statement, verify it contains the error pattern
- Confirm the statement would cause the specific PostgreSQL error
- Ensure the error keyword and context match the statement content

STEP 4: FALLBACK PATTERN MATCHING (If no spatial correlation possible)
- Systematically examine each statement for patterns matching the error
- Look for specific syntax issues, type mismatches, or constraint violations
- Pay attention to Oracle-specific constructs that don't work in PostgreSQL

ERROR PATTERN MATCHING CRITERIA:
- Data Type Issues: NUMBER vs NUMERIC/INTEGER, VARCHAR2 vs VARCHAR, etc.
- Syntax Differences: Oracle-specific functions, operators, keywords
- Constraint Violations: NOT NULL, CHECK, FOREIGN KEY, UNIQUE constraints
- Function/Procedure Issues: RETURN statements, parameter handling, OUT parameters
- Control Flow Issues: IF/ELSE, LOOP, EXCEPTION handling syntax differences
- Transaction Issues: COMMIT, ROLLBACK, SAVEPOINT handling
- Cursor Issues: OPEN, FETCH, CLOSE cursor operations
- Index/Optimization: Hints, index syntax differences

ENHANCED CONFIDENCE SCORING:
- 0.95-1.0: Exact spatial match (statement contains exact PostgreSQL error line content)
- 0.85-0.94: Strong spatial correlation with error pattern verification
- 0.75-0.84: Good pattern match with some spatial uncertainty
- 0.65-0.74: Moderate pattern match, requires validation
- 0.50-0.64: Weak pattern match, spatial correlation failed
- Below 0.5: No reliable match found, needs re-identification

IDENTIFICATION PRIORITY (Use in this order):
1. SPATIAL MATCH: Statement contains exact PostgreSQL error line content (Confidence: 0.95+)
2. SPATIAL + PATTERN: Statement contains error line + matches error pattern (Confidence: 0.85+)
3. PATTERN ONLY: Statement matches error pattern but no spatial correlation (Confidence: 0.75 max)
4. WEAK MATCH: Similar issues but uncertain correlation (Confidence: 0.65 max)

OUTPUT FORMAT (JSON):
{{
  "error_statement_number": <integer>,
  "confidence_score": <float between 0.0 and 1.0>,
  "reasoning": "<comprehensive analysis including: 1) Spatial correlation results (does statement contain exact error line?), 2) Error line content verification, 3) Pattern matching analysis, 4) Why this statement was chosen over others, 5) Specific technical evidence for the identification, 6) Confidence justification based on spatial vs pattern matching>",
  "spatial_match_found": true/false,
  "error_line_in_statement": "<exact error line content if found in statement, or 'NOT FOUND'>",
  "identification_method": "<'SPATIAL_CORRELATION' or 'PATTERN_MATCHING' or 'HYBRID'>"
}}

CRITICAL REQUIREMENTS:
- If PostgreSQL line number is available, MUST attempt spatial correlation first
- If spatial match found, confidence should be 0.95+
- If no spatial match found, confidence should be ≤ 0.75
- Provide specific evidence for spatial correlation or explain why it failed
- The statement number must be between 1 and {len(target_statements)}"""


def create_phase2_error_context_prompt(target_statements: List[str], identified_error_statement: int, error_message: str) -> str:
    """
    Phase 2: Create error context around the identified error statement.
    """
    statements_text = "\n".join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)])

    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. You have already identified that target statement #{identified_error_statement} is causing the deployment error.

Now create the ERROR CONTEXT around this identified error statement.

ERROR MESSAGE:
{error_message}

TARGET STATEMENTS:
{statements_text}

IDENTIFIED ERROR STATEMENT:
- Statement #{identified_error_statement} has been identified as the primary error statement

PHASE 2 ANALYSIS - ERROR CONTEXT CREATION:
Create a 3-statement context around the identified error statement.

STEP 1: VERIFY ERROR STATEMENT
- Confirm statement #{identified_error_statement} is the correct error statement
- Ensure it matches the error message details

STEP 2: CONTEXT BOUNDARY DETERMINATION
- Identify the statement immediately before the error (before_error)
- Identify the statement immediately after the error (after_error)
- Handle edge cases for first/last statements
- Recognize that some statements may be target database-specific with no source equivalent

STEP 3: CONTEXT VALIDATION
- Ensure the context provides meaningful surrounding information
- Verify the before/after statements are logically related to the error statement
- Confirm the context will be useful for error analysis and fixing

CONTEXT CREATION RULES:
1. before_error: Statement #{identified_error_statement - 1} (if exists, otherwise 0)
2. error_statement: Statement #{identified_error_statement}
3. after_error: Statement #{identified_error_statement + 1} (if exists, otherwise 0)

EDGE CASE HANDLING:
- If error statement is #1: before_error = 0, error_statement = 1, after_error = 2
- If error statement is last: before_error = last-1, error_statement = last, after_error = 0
- If only one statement: before_error = 0, error_statement = 1, after_error = 0

OUTPUT FORMAT (JSON):
{{
  "target_statements": [
    {{
      "target_statement_number": <integer for before_error (0 if none)>,
      "statement_type": "before_error"
    }},
    {{
      "target_statement_number": {identified_error_statement},
      "statement_type": "error_statement"
    }},
    {{
      "target_statement_number": <integer for after_error (0 if none)>,
      "statement_type": "after_error"
    }}
  ],
  "validation_notes": "<analysis including: 1) Confirmation of error statement accuracy, 2) Assessment of context relevance and completeness, 3) Evaluation of before/after statement logical relationship, 4) Any edge case handling applied, 5) Context quality assessment for error analysis>"
}}

IMPORTANT NOTES:
- The error_statement must be #{identified_error_statement}
- Use 0 for before_error or after_error if they don't exist
- Ensure all statement numbers are valid (between 0 and {len(target_statements)})
- Provide comprehensive validation notes"""
