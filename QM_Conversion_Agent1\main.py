import sys
import uuid
from typing import Dict, Any
from config import ConfigManager
from llm import (
    AzureOpenAILLM,
    OpenAILLM,
    AnthropicLLM,
    GroqLLM,
    GeminiLLM,
    OllamaLLM
)
from workflow import GraphBuilder
from dotenv import load_dotenv
load_dotenv()


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """
    Create and initialize a Language Model instance based on the specified provider.

    This function supports multiple LLM providers including Azure OpenAI, OpenAI, Anthropic,
    Groq, Gemini, and Ollama. Each provider is initialized with the appropriate configuration
    settings from the config manager.

    Args:
        provider: The LLM provider name (azure_openai, openai, anthropic, groq, gemini, ollama)
        config_manager: Configuration manager containing provider-specific settings

    Returns:
        An initialized instance of the appropriate LLM class ready for use

    Raises:
        ValueError: If the provider is not supported or recognized
    """
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    elif provider == "ollama":
        return OllamaLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")


def setup_application() -> Any:
    """
    Set up the application with configuration and initialize the Language Model.

    This function handles the complete application setup process including loading
    configuration settings and initializing the appropriate LLM provider based
    on the configuration.

    Returns:
        An initialized LLM instance ready for database migration workflows

    Raises:
        Exception: If LLM initialization fails
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"🔧 Attempting to initialize {llm_provider} LLM...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} client")
        return llm
    except Exception as e:
        print(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise


def run_workflow(llm: Any, source_code: str, target_code: str, deployment_error: str) -> Dict[str, Any]:
    """
    Execute the complete Oracle to PostgreSQL database migration workflow.

    This function orchestrates the entire migration process including error analysis,
    source mapping, statement conversion, and validation using AI-driven techniques.
    The workflow handles iterative improvements and maintains detailed tracking.

    Args:
        llm: Initialized Language Model instance for AI-driven analysis
        source_code: Original Oracle database code to be migrated
        target_code: PostgreSQL target code with potential deployment errors
        deployment_error: Error message from PostgreSQL deployment attempt

    Returns:
        Dictionary containing workflow execution results and final state
    """
    graph_builder = GraphBuilder(llm)
    graph_builder.setup_graph()
    graph_builder.save_graph_image(graph_builder.graph)


    # Create a unique thread ID for this workflow execution
    thread_id = f"thread_{uuid.uuid4()}"
    print(f"🔗 Using thread ID: {thread_id}")

    result = graph_builder.invoke_graph({
        "source_code": source_code,
        "target_code": target_code,
        "deployment_error": deployment_error,
        "iteration_count": 1  # Initialize iteration count to 1
    }, thread_id=thread_id)
    return result


def main():
    try:
        # Sample data for testing - in a real application, these would be provided by the user
        source_code = """
CREATE OR REPLACE  FUNCTION ""LAB"".""FN_GETPATIENTDETAILS"" (IN_UHID IN VARCHAR)
  RETURN VARCHAR2 IS
  V_PATIENTDETAILS VARCHAR(500);
BEGIN
  SELECT (SELECT TLM.TITLETYPE
            FROM EHIS.TITLEMASTER TLM
           WHERE P.TITLE = TLM.TITLECODE) || ' ' || P.FIRSTNAME || ' ' ||
         P.MIDDLENAME || ' ' || P.LASTNAME || ' ' ||
         (SELECT SM.SUFFIXNAME
            FROM EHIS.SUFFIXMASTER SM
           WHERE P.SUFIX = SM.SUFFIXCODE) || ' /' || LD.LOVDETAILVALUE || ' /' ||
         FLOOR(MONTHS_BETWEEN(SYSDATE, P.BIRTHDATE) / 12) || 'Yr' || ' ' ||
         floor(mod(months_between(sysdate, p.birthdate), 12)) || 'Mth' || ' ' ||
         trunc(sysdate -
               add_months(p.birthdate,
                          trunc(months_between(sysdate, p.birthdate) / 12) * 12 +
                          trunc(mod(months_between(sysdate, p.birthdate), 12)))) ||
         'Days'
  --TO_CHAR(P.BIRTHDATE,'DD-MON-YYYY')
    INTO V_PATIENTDETAILS
    FROM REGISTRATION.PATIENT P, EHIS.LOVDETAIL LD
   WHERE LD.LOVDETAILID = TO_NUMBER(P.GENDER)
     AND (P.EMERGENCYNO = IN_UHID OR P.PREREGISTRATIONNO = IN_UHID OR
         P.UHID = IN_UHID);
  RETURN V_PATIENTDETAILS;
END FN_GETPATIENTDETAILS;
        """

        target_code = """
set search_path to LAB;
create or replace function LAB.FN_GETPATIENTDETAILS(IN_UHID IN VARCHAR)
returns varchar
language plpgsql
security definer as $BODY$
declare
V_PATIENTDETAILS VARCHAR(500);
begin
set search_path to LAB;
SELECT(SELECT TLM.TITLETYPE
FROM EHIS.TITLEMASTER TLM
WHERE P.TITLE = TLM.TITLECODE)|| ' ' || P.FIRSTNAME || ' ' ||
P.MIDDLENAME || ' ' || P.LASTNAME || ' ' ||
(SELECT SM.SUFFIXNAME
FROM EHIS.SUFFIXMASTER SM
WHERE P.SUFIX = SM.SUFFIXCODE)|| ' /' || LD.LOVDETAILVALUE || ' /' ||
FLOOR(public.months_between(current_timestamp(0)::timestamp, P.BIRTHDATE)/ 12)|| 'Yr' || ' ' ||
floor(mod(public.months_between(current_timestamp(0)::timestamp, p.birthdate), 12))|| 'Mth' || ' ' ||
current_date -
p.birthdate + INTERVAL 'date_trunc('day',public.months_between(current_timestamp(0 Months'::timestamp, p.birthdate/ 12)* 12 +
mod(public.months_between(current_timestamp(0)::timestamp, p.birthdate::date , 12))))||
'Days'
--TO_CHAR(P.BIRTHDATE,'DD-MON-YYYY')


into strict V_PATIENTDETAILS
FROM REGISTRATION.PATIENT P, EHIS.LOVDETAIL LD
WHERE LD.LOVDETAILID = TO_NUMBER(P.GENDER)
AND(P.EMERGENCYNO = IN_UHID OR P.PREREGISTRATIONNO = IN_UHID OR
P.UHID = IN_UHID);
RETURN V_PATIENTDETAILS;
end;
$BODY$;
               """

        deployment_error = """
syntax error at or near "',public.months_between(current_timestamp(0 Months'"
LINE 20: p.birthdate + INTERVAL 'date_trunc('day',public.months_betwe...

        """

        llm = setup_application()
        run_workflow(llm, source_code, target_code, deployment_error)

        print("\n🎉 Execution completed successfully!")

    except ValueError as e:
        print(f"\nConfiguration Error: {str(e)}")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected Error: {str(e)}")
        print("\nPlease report this issue with the error details above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
